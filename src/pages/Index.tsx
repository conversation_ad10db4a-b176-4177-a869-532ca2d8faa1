
import React from 'react';
import Navbar from '@/components/Navbar';
import MarketTicker from '@/components/MarketTicker';
import HeroSection from '@/components/HeroSection';
import InsightsSection from '@/components/InsightsSection';
import NewsSection from '@/components/NewsSection';
import EducationSection from '@/components/EducationSection';
import CaseStudiesSection from '@/components/CaseStudiesSection';
import Footer from '@/components/Footer';
import UserFeedbackForm from '@/components/UserFeedbackForm';

const Index = () => {
  return (
    <div className="min-h-screen flex flex-col">
      <MarketTicker />
      <Navbar />
      <main className="flex-grow">
        <HeroSection />
        <InsightsSection />
        <NewsSection />
        <div className="container py-12">
          <EducationSection />
        </div>
        <CaseStudiesSection />
      </main>
      <UserFeedbackForm/>
      <Footer />
    </div>
  );
};

export default Index;
