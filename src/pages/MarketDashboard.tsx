import React from 'react';
import Navbar from '@/components/Navbar';
import MarketTicker from '@/components/MarketTicker';
import MarketDashboard from '@/components/MarketDashboard';
import Footer from '@/components/Footer';

const MarketDashboardPage: React.FC = () => {
  return (
    <div className="min-h-screen flex flex-col">
      <MarketTicker />
      <Navbar />
      <main className="flex-grow">
        <div className="container py-8">
          {/* Page Header */}
          <div className="text-center mb-8">
            <h1 className="text-4xl font-bold mb-4">Real-Time Market Dashboard</h1>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              Stay updated with live market data, track your favorite stocks, and discover market trends with our comprehensive dashboard.
            </p>
          </div>
          
          {/* Market Dashboard Component */}
          <MarketDashboard />
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default MarketDashboardPage;
